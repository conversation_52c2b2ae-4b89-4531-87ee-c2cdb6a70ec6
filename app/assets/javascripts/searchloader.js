var MR = MR || {};
MR = (function (window, document, Mirraw) {
  Mirraw.searchloader = {
    initSearchLoader: function () {
      const $form = $('.search-bar-form');
      const $overlay = $('.overlay');
      const $loader = $('.loader_main');
      function showLoader() {
        $overlay.show();
        $loader.show();
      }
      function hideLoader() {
        $overlay.hide();
        $loader.hide();
      }
      $(document).ready(function() {
        $form.on('submit', function (event) {
          const searchInput = $('#search_input');
          const searchValue = searchInput.val().trim();

          // Check if input is empty or doesn't meet minimum length requirement
          if (searchValue === '' || searchValue.length < 3) {
            // Let browser handle validation (show native validation message)
            return true;
          }

          showLoader();
          // Don't call $form.submit() again as it would cause infinite loop
        });

        $(document).on('click', '.unbxd-as-keysuggestion', function () {
          showLoader();
        });
        $(document).on('click', '.unbxd-as-popular-product-name', function () {
          showLoader();
        });

        $form.on('keydown', function (event) {
          if (event.key === 'Enter') {
            const searchInput = $('#search_input');
            const searchValue = searchInput.val().trim();

            // Check if input is empty or doesn't meet minimum length requirement
            if (searchValue === '' || searchValue.length < 3) {
              // Prevent form submission and let browser show validation message
              event.preventDefault();
              // Trigger form validation by attempting to submit
              searchInput[0].reportValidity();
              return false;
            }

            showLoader();
            // Let the form submit naturally
          }
        });
        hideLoader();
      });
      window.addEventListener('pageshow', function () {
        hideLoader();
      });
    },
  };
  return Mirraw;
})(this, this.document, MR);
afterWindowOrTrubolinksLoad(function(){
  MR.searchloader.initSearchLoader();
});