.row
  .designer_info_pane
    #master_image
      - if @designer.try(:photo).present?
        = image_tag(@designer.photo(:small), :alt => @designer.name, :width => '100%')
      - else 
        %p= 'No photo uploaded'
.row
  %ul.facet_follow
    %li.published_count_block
      %strong.bignumber= @results['results']
      .softtext Designs
    %li.followers_count_block
      %strong.bignumber= @designer.followers_count
      .softtext= @designer.followers_count > 1 ? 'Followers' : 'Follower'
  - if account_signed_in? && current_account.provider == "facebook"
    - form_class = "signedin"
  - else
    - form_class = "not-signedin"